"use client";

import { useState, useCallback } from "react";
import { <PERSON><PERSON>, buttonVariants } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { ImageIcon, MousePointerClickIcon, XIcon, DownloadIcon, RotateCcwIcon, ChevronDownIcon } from "lucide-react";
import { cn, getNanoId } from "@/lib/utils";
import { Dropzone, DropzoneContent, DropzoneEmptyState } from "@/components/ui/kibo-ui/dropzone";
import { ImageCrop, ImageCropApply, ImageCropContent, ImageCropReset } from "@/components/ui/kibo-ui/image-crop";
import type { PercentCrop, PixelCrop } from "react-image-crop";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";

// 常见宽高比选项
const ASPECT_RATIOS = [
	{ label: "Free", value: "free", ratio: undefined },
	{ label: "1:1 (Square)", value: "1:1", ratio: 1 },
	{ label: "4:3", value: "4:3", ratio: 4 / 3 },
	{ label: "3:4", value: "3:4", ratio: 3 / 4 },
	{ label: "16:9", value: "16:9", ratio: 16 / 9 },
	{ label: "9:16", value: "9:16", ratio: 9 / 16 },
	{ label: "3:2", value: "3:2", ratio: 3 / 2 },
	{ label: "2:3", value: "2:3", ratio: 2 / 3 },
];

export function CropImageClient({ hookText = "or, drag and drop an image here" }: { hookText?: string }) {
	const [selectedFile, setSelectedFile] = useState<File | null>(null);

	// 裁剪相关状态
	const [crop, setCrop] = useState<PercentCrop>();
	const [completedCrop, setCompletedCrop] = useState<PixelCrop | null>(null);
	const [selectedAspectRatio, setSelectedAspectRatio] = useState<{
		label: string;
		value: string;
		ratio: number | undefined;
	}>(ASPECT_RATIOS[0]);

	// 图片尺寸状态
	const [imageSize, setImageSize] = useState({ width: 0, height: 0 });
	const [cropSize, setCropSize] = useState({ width: 0, height: 0 });
	const [cropPosition, setCropPosition] = useState({ x: 0, y: 0 });

	const handleLocalFileDrop = async (files: File[]) => {
		if (!files || files.length === 0) return;
		setSelectedFile(files[0]);
	};

	const handleReset = () => {
		setSelectedFile(null);
		setCrop(undefined);
		setCompletedCrop(null);
		setSelectedAspectRatio(ASPECT_RATIOS[0]);
		setImageSize({ width: 0, height: 0 });
		setCropSize({ width: 0, height: 0 });
		setCropPosition({ x: 0, y: 0 });
	};

	// 处理裁剪变化
	const handleCropChange = useCallback(
		(pixelCrop: PixelCrop, percentCrop: PercentCrop) => {
			setCrop(percentCrop);

			if (pixelCrop) {
				// 从pixelCrop和percentCrop推断图片尺寸
				if (percentCrop.width > 0 && percentCrop.height > 0) {
					const imgWidth = Math.round(pixelCrop.width / (percentCrop.width / 100));
					const imgHeight = Math.round(pixelCrop.height / (percentCrop.height / 100));

					if (imgWidth !== imageSize.width || imgHeight !== imageSize.height) {
						setImageSize({ width: imgWidth, height: imgHeight });
					}
				}

				const newCropSize = {
					width: Math.round(pixelCrop.width),
					height: Math.round(pixelCrop.height),
				};
				const newCropPosition = {
					x: Math.round(pixelCrop.x),
					y: Math.round(pixelCrop.y),
				};

				setCropSize(newCropSize);
				setCropPosition(newCropPosition);
			}
		},
		[imageSize],
	);

	// 处理裁剪完成
	const handleCropComplete = useCallback(async (pixelCrop: PixelCrop) => {
		setCompletedCrop(pixelCrop);

		// 更新裁剪尺寸和位置
		const newCropSize = {
			width: Math.round(pixelCrop.width),
			height: Math.round(pixelCrop.height),
		};
		const newCropPosition = {
			x: Math.round(pixelCrop.x),
			y: Math.round(pixelCrop.y),
		};

		setCropSize(newCropSize);
		setCropPosition(newCropPosition);
	}, []);

	// 处理宽高比变化
	const handleAspectRatioChange = (aspectRatio: any) => {
		console.log(aspectRatio);
		setSelectedAspectRatio(aspectRatio);

		// 如果有图片尺寸，立即应用新的aspect ratio
		if (imageSize.width && imageSize.height && aspectRatio.ratio) {
			// 计算新的裁剪区域以符合选定的宽高比
			const centerX = 50;
			const centerY = 50;

			// 先计算一个合适的基础尺寸，确保最终像素尺寸符合宽高比
			const maxCropSize = Math.min(imageSize.width, imageSize.height) * 0.8; // 80% 的最小边
			let pixelWidth: number;
			let pixelHeight: number;

			// 根据宽高比计算像素尺寸
			if (aspectRatio.ratio >= 1) {
				// 宽度大于等于高度的情况
				pixelWidth = maxCropSize;
				pixelHeight = pixelWidth / aspectRatio.ratio;
			} else {
				// 高度大于宽度的情况
				pixelHeight = maxCropSize;
				pixelWidth = pixelHeight * aspectRatio.ratio;
			}

			// 确保不超出图片边界
			if (pixelWidth > imageSize.width * 0.9) {
				pixelWidth = imageSize.width * 0.9;
				pixelHeight = pixelWidth / aspectRatio.ratio;
			}
			if (pixelHeight > imageSize.height * 0.9) {
				pixelHeight = imageSize.height * 0.9;
				pixelWidth = pixelHeight * aspectRatio.ratio;
			}

			// 转换为百分比
			const newWidth = (pixelWidth / imageSize.width) * 100;
			const newHeight = (pixelHeight / imageSize.height) * 100;

			const newCrop: PercentCrop = {
				unit: "%",
				x: centerX - newWidth / 2,
				y: centerY - newHeight / 2,
				width: newWidth,
				height: newHeight,
			};

			setCrop(newCrop);

			// 更新裁剪尺寸和位置
			const pixelX = Math.round((newCrop.x / 100) * imageSize.width);
			const pixelY = Math.round((newCrop.y / 100) * imageSize.height);

			setCropSize({ width: Math.round(pixelWidth), height: Math.round(pixelHeight) });
			setCropPosition({ x: pixelX, y: pixelY });
		}
	};

	// 处理裁剪尺寸变化
	const handleCropSizeChange = (field: "width" | "height", value: string) => {
		const numValue = parseInt(value) || 0;
		if (numValue < 0 || numValue > (field === "width" ? imageSize.width : imageSize.height)) return;

		let newWidth = field === "width" ? numValue : cropSize.width;
		let newHeight = field === "height" ? numValue : cropSize.height;

		// 如果有选择的aspect ratio，自动调整另一个维度来保持比例
		if (selectedAspectRatio.ratio) {
			if (field === "width") {
				// 用户修改了width，根据aspect ratio计算新的height
				newHeight = Math.round(newWidth / selectedAspectRatio.ratio);
				// 确保height不超出图片边界
				if (newHeight > imageSize.height) {
					newHeight = imageSize.height;
					newWidth = Math.round(newHeight * selectedAspectRatio.ratio);
				}
			} else {
				// 用户修改了height，根据aspect ratio计算新的width
				newWidth = Math.round(newHeight * selectedAspectRatio.ratio);
				// 确保width不超出图片边界
				if (newWidth > imageSize.width) {
					newWidth = imageSize.width;
					newHeight = Math.round(newWidth / selectedAspectRatio.ratio);
				}
			}
		}

		setCropSize({ width: newWidth, height: newHeight });

		// 更新裁剪区域
		if (crop && imageSize.width && imageSize.height) {
			const newCrop = { ...crop };
			newCrop.width = (newWidth / imageSize.width) * 100;
			newCrop.height = (newHeight / imageSize.height) * 100;

			// 确保裁剪区域不会超出图片边界，如果超出则调整位置
			const maxX = imageSize.width - newWidth;
			const maxY = imageSize.height - newHeight;

			let newX = cropPosition.x;
			let newY = cropPosition.y;

			if (newX > maxX) {
				newX = Math.max(0, maxX);
				newCrop.x = (newX / imageSize.width) * 100;
				setCropPosition((prev) => ({ ...prev, x: newX }));
			}

			if (newY > maxY) {
				newY = Math.max(0, maxY);
				newCrop.y = (newY / imageSize.height) * 100;
				setCropPosition((prev) => ({ ...prev, y: newY }));
			}

			setCrop(newCrop);
		}
	};

	// 处理裁剪位置变化
	const handleCropPositionChange = (field: "x" | "y", value: string) => {
		const numValue = parseInt(value) || 0;
		const maxValue = field === "x" ? imageSize.width - cropSize.width : imageSize.height - cropSize.height;

		if (numValue < 0 || numValue > maxValue) return;

		setCropPosition((prev) => ({ ...prev, [field]: numValue }));

		// 更新裁剪区域
		if (crop && imageSize.width && imageSize.height) {
			const newCrop = { ...crop };
			if (field === "x") {
				newCrop.x = (numValue / imageSize.width) * 100;
			} else {
				newCrop.y = (numValue / imageSize.height) * 100;
			}
			setCrop(newCrop);
		}
	};

	// 处理裁剪图片 - 直接下载
	const handleCropImage = (croppedImageUrl: string) => {
		// 直接下载，不设置预览
		const link = document.createElement("a");
		link.download = `cropped-${getNanoId()}.png`;
		link.href = croppedImageUrl;
		link.click();
	};

	return (
		<>
			<div className={cn("bg-muted mx-auto rounded-2xl border shadow-lg")}>
				<div className="flex h-full w-full flex-col gap-2 p-2">
					{selectedFile ? (
						<div className="flex gap-6">
							<ImageCrop
								aspect={selectedAspectRatio.ratio}
								file={selectedFile}
								onChange={handleCropChange}
								onComplete={handleCropComplete}
								onCrop={handleCropImage}
								crop={crop}
							>
								{/* 左侧：图片裁剪区域 */}
								<div className="flex h-full flex-1 flex-col">
									<ImageCropContent className="h-full" />
								</div>

								{/* 右侧：设置面板 */}
								<div className="w-80 space-y-6 rounded-lg bg-white p-4">
									{/* Crop Rectangle 设置 */}
									<div className="space-y-4">
										<h3 className="text-lg font-semibold">Crop Rectangle</h3>

										{/* Aspect Ratio */}
										<div className="space-y-2">
											<Label htmlFor="aspect-ratio">Aspect Ratio</Label>

											<DropdownMenu>
												<DropdownMenuTrigger
													className={cn(
														"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-full items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",
													)}
												>
													{selectedAspectRatio.label}
													<ChevronDownIcon className="-me-1 opacity-60" size={16} aria-hidden="true" />
												</DropdownMenuTrigger>
												<DropdownMenuContent className="min-w-(--radix-dropdown-menu-trigger-width)">
													{ASPECT_RATIOS.map((ar) => (
														<DropdownMenuItem
															key={ar.value}
															onClick={() => {
																handleAspectRatioChange(ar);
															}}
															className={cn(
																"cursor-pointer",
																selectedAspectRatio.value === ar.value
																	? "bg-blue-500 text-white focus:bg-blue-500 focus:text-white"
																	: "focus:bg-blue-100",
															)}
														>
															{ar.label}
														</DropdownMenuItem>
													))}
												</DropdownMenuContent>
											</DropdownMenu>
										</div>

										{/* Width & Height */}
										<div className="grid grid-cols-2 gap-3">
											<div className="space-y-2">
												<Label htmlFor="crop-width">Width (px)</Label>
												<Input
													id="crop-width"
													type="number"
													value={cropSize.width}
													onChange={(e) => handleCropSizeChange("width", e.target.value)}
													min={1}
													max={imageSize.width}
												/>
											</div>
											<div className="space-y-2">
												<Label htmlFor="crop-height">Height (px)</Label>
												<Input
													id="crop-height"
													type="number"
													value={cropSize.height}
													onChange={(e) => handleCropSizeChange("height", e.target.value)}
													min={1}
													max={imageSize.height}
												/>
											</div>
										</div>
									</div>

									{/* Crop Position 设置 */}
									<div className="space-y-4">
										<h3 className="text-lg font-semibold">Crop Position</h3>

										<div className="grid grid-cols-2 gap-3">
											<div className="space-y-2">
												<Label htmlFor="crop-x">Position X (px)</Label>
												<Input
													id="crop-x"
													type="number"
													value={cropPosition.x}
													onChange={(e) => handleCropPositionChange("x", e.target.value)}
													min={0}
													max={imageSize.width - cropSize.width}
												/>
											</div>
											<div className="space-y-2">
												<Label htmlFor="crop-y">Position Y (px)</Label>
												<Input
													id="crop-y"
													type="number"
													value={cropPosition.y}
													onChange={(e) => handleCropPositionChange("y", e.target.value)}
													min={0}
													max={imageSize.height - cropSize.height}
												/>
											</div>
										</div>
									</div>

									{/* 操作按钮 */}
									<div className="space-y-3">
										<div className="flex gap-2">
											<ImageCropReset asChild>
												<Button variant="outline" className="flex-1">
													<RotateCcwIcon className="mr-2 size-4" />
													Reset
												</Button>
											</ImageCropReset>

											<ImageCropApply asChild>
												<Button className="flex-1">
													<DownloadIcon className="mr-2 size-4" />
													Crop & Download
												</Button>
											</ImageCropApply>
										</div>

										<Button onClick={handleReset} variant="ghost" className="w-full">
											<XIcon className="mr-2 size-4" />
											Select New Image
										</Button>
									</div>
								</div>
							</ImageCrop>
						</div>
					) : (
						<div className="h-full flex-1">
							<Dropzone
								multiple={false}
								maxFiles={1}
								onDrop={handleLocalFileDrop}
								accept={{ "image/*": [] }}
								onError={console.error}
								className={cn(
									"hover:border-primary h-full min-h-[256px] cursor-pointer rounded-xl border-dashed border-zinc-300 bg-white whitespace-pre-wrap hover:bg-white",
								)}
							>
								<DropzoneEmptyState>
									<>
										<div className="bg-muted flex items-center justify-center rounded-full border p-2">
											<ImageIcon className="size-6 text-zinc-700" strokeWidth={1.5} />
										</div>
										<div className="full mt-1 space-y-1 text-sm font-normal">
											<p className={cn(buttonVariants({ size: "lg" }), "mt-2 h-12 bg-blue-500 hover:bg-blue-500/90 has-[>svg]:px-8")}>
												<MousePointerClickIcon className="" />
												Select Image
											</p>
											<p className="w-full text-sm font-normal text-zinc-800">{hookText}</p>
										</div>
									</>
								</DropzoneEmptyState>
								<DropzoneContent />
							</Dropzone>
						</div>
					)}
				</div>
			</div>
		</>
	);
}
